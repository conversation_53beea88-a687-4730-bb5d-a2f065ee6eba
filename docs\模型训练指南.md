# 🚀 模型训练指南

## 📋 目录

1. [训练前准备](#训练前准备)
2. [数据集准备](#数据集准备)
3. [人体检测模型训练](#人体检测模型训练)
4. [去烟算法模型训练](#去烟算法模型训练)
5. [训练监控和调试](#训练监控和调试)
6. [模型评估和优化](#模型评估和优化)
7. [常见问题解决](#常见问题解决)

## 🔧 训练前准备

### 1. 环境检查
```bash
# 检查环境配置
python src/environment_check.py
```

### 2. 数据集分析
```bash
# 分析数据集结构和统计信息
python src/data_analysis.py
```

### 3. 创建必要目录
```bash
# 创建训练相关目录
mkdir -p models/detection models/dehazing logs results/training
```

## 📊 数据集准备

### 当前数据集状态
- **数据集名称**: wuxi_video_2
- **总图像数**: 3,187张
- **训练集**: 2,245张标注图像
- **验证集**: 942张标注图像
- **类别**: person (人体)

### 数据集结构
```
wuxi_video_2/
├── image/              # 原始图像
│   ├── wuxi_2_0000.jpg
│   ├── wuxi_2_0001.jpg
│   └── ...
├── labels/             # YOLO格式标注
│   ├── train/          # 训练集标注
│   │   ├── wuxi_2_0000.txt
│   │   └── ...
│   └── val/            # 验证集标注
│       ├── wuxi_2_0003.txt
│       └── ...
└── classes.txt.txt     # 类别文件
```

### 数据集验证
```bash
# 验证数据集完整性
python -c "
import os
image_dir = 'wuxi_video_2/image'
train_dir = 'wuxi_video_2/labels/train'
val_dir = 'wuxi_video_2/labels/val'

images = len([f for f in os.listdir(image_dir) if f.endswith('.jpg')])
train_labels = len([f for f in os.listdir(train_dir) if f.endswith('.txt')])
val_labels = len([f for f in os.listdir(val_dir) if f.endswith('.txt')])

print(f'图像总数: {images}')
print(f'训练标注: {train_labels}')
print(f'验证标注: {val_labels}')
print(f'标注覆盖率: {(train_labels + val_labels) / images * 100:.1f}%')
"
```

## 🎯 人体检测模型训练

### 1. 快速开始训练

#### 方法一：使用项目训练脚本
```bash
# 使用默认配置训练
python src/train_detection.py
```

#### 方法二：使用YOLOv8命令行
```bash
# 安装ultralytics（如果未安装）
pip install ultralytics

# 开始训练
yolo detect train data=configs/dataset.yaml model=yolov8n.pt epochs=100 imgsz=640
```

### 2. 自定义训练配置

#### 创建数据集配置文件
```bash
# 创建YOLO格式的数据集配置
cat > configs/dataset.yaml << EOF
# 数据集配置
path: wuxi_video_2  # 数据集根目录
train: image  # 训练图像目录
val: image    # 验证图像目录

# 类别
nc: 1  # 类别数量
names: ['person']  # 类别名称

# 训练/验证分割
train_labels: labels/train
val_labels: labels/val
EOF
```

#### 高级训练配置
```python
# 创建自定义训练脚本
from ultralytics import YOLO

# 初始化模型
model = YOLO('yolov8n.pt')  # 或 yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt

# 训练配置
results = model.train(
    data='configs/dataset.yaml',
    epochs=100,                 # 训练轮数
    imgsz=640,                 # 图像尺寸
    batch=16,                  # 批次大小
    lr0=0.01,                  # 初始学习率
    weight_decay=0.0005,       # 权重衰减
    momentum=0.937,            # 动量
    patience=50,               # 早停耐心值
    save_period=10,            # 保存周期
    device='cpu',              # 设备 ('cpu' 或 'cuda')
    workers=4,                 # 数据加载线程数
    project='results',         # 项目目录
    name='detection_training', # 实验名称
    exist_ok=True,            # 覆盖现有项目
    pretrained=True,          # 使用预训练权重
    optimizer='SGD',          # 优化器
    verbose=True,             # 详细输出
    seed=42,                  # 随机种子
    deterministic=True,       # 确定性训练
    single_cls=True,          # 单类别训练
    rect=False,               # 矩形训练
    cos_lr=False,             # 余弦学习率调度
    close_mosaic=10,          # 关闭马赛克增强的轮数
    resume=False,             # 恢复训练
    amp=True,                 # 自动混合精度
    fraction=1.0,             # 数据集使用比例
    profile=False,            # 性能分析
    freeze=None,              # 冻结层数
)
```

### 3. 训练过程监控

#### 实时监控
```bash
# 查看训练日志
tail -f results/detection_training/train.log

# 使用TensorBoard监控（如果安装）
tensorboard --logdir results/detection_training
```

#### 训练指标
- **损失函数**: box_loss, cls_loss, dfl_loss
- **精度指标**: Precision, Recall, mAP50, mAP50-95
- **学习率**: 动态调整的学习率曲线

### 4. 模型验证和测试
```python
# 验证训练好的模型
from ultralytics import YOLO

# 加载训练好的模型
model = YOLO('results/detection_training/weights/best.pt')

# 在验证集上评估
results = model.val(data='configs/dataset.yaml')

# 打印评估结果
print(f"mAP50: {results.box.map50:.3f}")
print(f"mAP50-95: {results.box.map:.3f}")
print(f"Precision: {results.box.mp:.3f}")
print(f"Recall: {results.box.mr:.3f}")

# 在测试图像上预测
results = model.predict('wuxi_video_2/image/wuxi_2_0000.jpg', save=True)
```

## 🌫️ 去烟算法模型训练

### 1. 基础训练
```bash
# 使用默认配置训练去烟模型
python src/train_dehazing.py
```

### 2. 自定义训练配置
```python
# 修改训练配置
config = {
    "device": "cpu",           # 或 "cuda" 如果有GPU
    "batch_size": 4,           # 批次大小
    "epochs": 50,              # 训练轮数
    "learning_rate": 0.001,    # 学习率
    "weight_decay": 1e-4,      # 权重衰减
    "min_lr": 1e-6,           # 最小学习率
    "l1_weight": 1.0,         # L1损失权重
    "ssim_weight": 0.1,       # SSIM损失权重
    "num_workers": 2,         # 数据加载线程数
    "seed": 42                # 随机种子
}

# 开始训练
from src.train_dehazing import DehazingTrainer
trainer = DehazingTrainer(config)
trainer.train()
```

### 3. 去烟模型特点
- **网络架构**: AOD-Net (All-in-One Dehazing Network)
- **损失函数**: L1损失 + SSIM损失
- **数据增强**: 合成雾霾数据
- **训练策略**: 余弦退火学习率调度

## 📈 训练监控和调试

### 1. 训练日志分析
```python
# 分析训练历史
import json
import matplotlib.pyplot as plt

# 读取训练历史
with open('logs/detection_training_history.json', 'r') as f:
    history = json.load(f)

# 绘制损失曲线
plt.figure(figsize=(12, 4))

plt.subplot(1, 3, 1)
plt.plot(history['train_loss'])
plt.plot(history['val_loss'])
plt.title('Loss')
plt.legend(['Train', 'Val'])

plt.subplot(1, 3, 2)
plt.plot(history['precision'])
plt.plot(history['recall'])
plt.title('Precision & Recall')
plt.legend(['Precision', 'Recall'])

plt.subplot(1, 3, 3)
plt.plot(history['map50'])
plt.title('mAP@0.5')

plt.tight_layout()
plt.savefig('results/training_curves.png')
plt.show()
```

### 2. 性能监控
```python
# 监控系统资源使用
import psutil
import time

def monitor_training():
    while True:
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        print(f"CPU: {cpu_percent}%, Memory: {memory.percent}%")
        time.sleep(10)

# 在后台运行监控
import threading
monitor_thread = threading.Thread(target=monitor_training)
monitor_thread.daemon = True
monitor_thread.start()
```

### 3. 训练问题诊断

#### 常见问题及解决方案

**问题1: 训练速度慢**
```python
# 解决方案：
# 1. 减小批次大小
batch_size = 8  # 从16减少到8

# 2. 减小图像尺寸
imgsz = 416  # 从640减少到416

# 3. 使用更小的模型
model = YOLO('yolov8n.pt')  # 使用nano版本

# 4. 减少数据加载线程
workers = 2  # 从4减少到2
```

**问题2: 内存不足**
```python
# 解决方案：
# 1. 减小批次大小
batch_size = 2

# 2. 启用梯度累积
accumulate = 4  # 累积4个批次后更新

# 3. 使用混合精度训练
amp = True
```

**问题3: 训练不收敛**
```python
# 解决方案：
# 1. 调整学习率
lr0 = 0.001  # 降低初始学习率

# 2. 增加训练轮数
epochs = 200

# 3. 使用预训练模型
pretrained = True

# 4. 检查数据质量
# 确保标注正确，图像质量良好
```

## 🎯 模型评估和优化

### 1. 模型性能评估
```python
# 详细评估脚本
from ultralytics import YOLO
import cv2
import numpy as np

def evaluate_model(model_path, test_images):
    model = YOLO(model_path)
    
    total_time = 0
    total_images = 0
    
    for image_path in test_images:
        # 加载图像
        image = cv2.imread(image_path)
        
        # 推理计时
        start_time = time.time()
        results = model.predict(image, verbose=False)
        end_time = time.time()
        
        total_time += (end_time - start_time)
        total_images += 1
        
        # 分析结果
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                print(f"图像: {image_path}")
                print(f"检测到 {len(boxes)} 个目标")
                for box in boxes:
                    conf = box.conf.item()
                    print(f"  置信度: {conf:.3f}")
    
    avg_time = total_time / total_images
    fps = 1 / avg_time
    
    print(f"\n性能统计:")
    print(f"平均处理时间: {avg_time*1000:.2f}ms")
    print(f"平均FPS: {fps:.1f}")
    
    return avg_time, fps

# 运行评估
test_images = ['wuxi_video_2/image/wuxi_2_0000.jpg']  # 添加更多测试图像
evaluate_model('results/detection_training/weights/best.pt', test_images)
```

### 2. 模型优化策略

#### 模型压缩
```python
# 模型量化
from ultralytics import YOLO

model = YOLO('results/detection_training/weights/best.pt')

# 导出为ONNX格式（更快推理）
model.export(format='onnx', dynamic=True, simplify=True)

# 导出为TensorRT格式（NVIDIA GPU加速）
# model.export(format='engine', device=0)
```

#### 超参数优化
```python
# 使用Optuna进行超参数搜索
import optuna

def objective(trial):
    # 定义超参数搜索空间
    lr0 = trial.suggest_float('lr0', 1e-5, 1e-1, log=True)
    weight_decay = trial.suggest_float('weight_decay', 1e-6, 1e-2, log=True)
    momentum = trial.suggest_float('momentum', 0.8, 0.99)
    
    # 训练模型
    model = YOLO('yolov8n.pt')
    results = model.train(
        data='configs/dataset.yaml',
        epochs=50,
        lr0=lr0,
        weight_decay=weight_decay,
        momentum=momentum,
        verbose=False
    )
    
    # 返回验证mAP作为优化目标
    return results.results_dict['metrics/mAP50(B)']

# 运行优化
study = optuna.create_study(direction='maximize')
study.optimize(objective, n_trials=20)

print("最佳超参数:")
print(study.best_params)
```

## ❓ 常见问题解决

### Q1: 如何处理数据不平衡？
```python
# 解决方案：
# 1. 使用类别权重
class_weights = [1.0]  # 调整权重

# 2. 数据增强
augment = True
mosaic = 1.0
mixup = 0.1

# 3. 焦点损失
# 在训练配置中启用焦点损失
```

### Q2: 如何提高小目标检测？
```python
# 解决方案：
# 1. 增大输入图像尺寸
imgsz = 1280  # 从640增加到1280

# 2. 使用多尺度训练
multiscale = True

# 3. 调整锚框尺寸
# 分析数据集中目标尺寸分布
```

### Q3: 如何加速训练？
```python
# 解决方案：
# 1. 使用更快的数据加载
workers = 8  # 增加线程数
pin_memory = True

# 2. 启用编译优化
torch.compile = True

# 3. 使用分布式训练
# DDP (Distributed Data Parallel)
```

### Q4: 训练中断如何恢复？
```python
# 恢复训练
model = YOLO('results/detection_training/weights/last.pt')
results = model.train(resume=True)
```

---

## 🎉 训练完成后的步骤

1. **模型验证**: 在测试集上评估模型性能
2. **模型部署**: 将训练好的模型集成到系统中
3. **性能测试**: 测试实际应用场景的性能
4. **持续优化**: 根据实际使用情况继续优化模型

**下一步**: 查看 [模型部署指南](模型部署指南.md) 了解如何部署训练好的模型。
